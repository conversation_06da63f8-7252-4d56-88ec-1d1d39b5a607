import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'page/everhome_ecotracker_page.dart';

/// 测试 EcoTracker 页面的简单入口
class TestEcoTrackerApp extends StatelessWidget {
  const TestEcoTrackerApp({super.key});

  @override
  Widget build(BuildContext context) {
    return ScreenUtilInit(
      designSize: const Size(375, 812),
      minTextAdapt: true,
      splitScreenMode: true,
      builder: (context, child) {
        return MaterialApp(
          title: 'EcoTracker Test',
          theme: ThemeData(
            primarySwatch: Colors.blue,
            useMaterial3: true,
          ),
          home: Scaffold(
            appBar: AppBar(
              title: const Text('EcoTracker 测试'),
              backgroundColor: Colors.blue,
              foregroundColor: Colors.white,
            ),
            body: Center(
              child: ElevatedButton(
                onPressed: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const EverHomeEcotrackerPage(),
                    ),
                  );
                },
                child: const Text('打开 EcoTracker 扫描页面'),
              ),
            ),
          ),
        );
      },
    );
  }
}

/// 测试入口函数
void main() {
  runApp(const TestEcoTrackerApp());
}
