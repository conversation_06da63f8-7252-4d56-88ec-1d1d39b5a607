import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'page/everhome_ecotracker_page.dart';

/// 测试 EcoTracker 页面的简单入口
class TestEcoTrackerApp extends StatelessWidget {
  const TestEcoTrackerApp({super.key});

  @override
  Widget build(BuildContext context) {
    return ScreenUtilInit(
      designSize: const Size(375, 812),
      minTextAdapt: true,
      splitScreenMode: true,
      builder: (context, child) {
        return MaterialApp(
          title: 'EcoTracker Test',
          theme: ThemeData(
            primarySwatch: Colors.blue,
            useMaterial3: true,
          ),
          home: const EverHomeEcotrackerPage(),
        );
      },
    );
  }
}

/// 测试入口函数
void main() {
  runApp(const TestEcoTrackerApp());
}
