import 'package:equatable/equatable.dart';

class EcoTrackerDeviceModel extends Equatable {
  final String id;
  final String name;
  final String ipAddress;
  final int port;
  final String? macAddress;
  final String? version;
  final String? model;
  final DateTime discoveredAt;
  final int rssi; // 信号强度
  final Map<String, dynamic>? additionalInfo;

  const EcoTrackerDeviceModel({
    required this.id,
    required this.name,
    required this.ipAddress,
    required this.port,
    this.macAddress,
    this.version,
    this.model,
    required this.discoveredAt,
    this.rssi = 0,
    this.additionalInfo,
  });

  factory EcoTrackerDeviceModel.fromMdnsRecord({
    required String serviceName,
    required String hostname,
    required String ipAddress,
    required int port,
    Map<String, String>? txtRecords,
  }) {
    // 从服务名称中提取设备ID
    final deviceId = serviceName.split('.').first;
    
    // 从TXT记录中提取设备信息
    final name = txtRecords?['name'] ?? 'EcoTracker Device';
    final macAddress = txtRecords?['mac'];
    final version = txtRecords?['version'];
    final model = txtRecords?['model'] ?? 'EcoTracker';

    return EcoTrackerDeviceModel(
      id: deviceId,
      name: name,
      ipAddress: ipAddress,
      port: port,
      macAddress: macAddress,
      version: version,
      model: model,
      discoveredAt: DateTime.now(),
      additionalInfo: txtRecords?.cast<String, dynamic>(),
    );
  }

  EcoTrackerDeviceModel copyWith({
    String? id,
    String? name,
    String? ipAddress,
    int? port,
    String? macAddress,
    String? version,
    String? model,
    DateTime? discoveredAt,
    int? rssi,
    Map<String, dynamic>? additionalInfo,
  }) {
    return EcoTrackerDeviceModel(
      id: id ?? this.id,
      name: name ?? this.name,
      ipAddress: ipAddress ?? this.ipAddress,
      port: port ?? this.port,
      macAddress: macAddress ?? this.macAddress,
      version: version ?? this.version,
      model: model ?? this.model,
      discoveredAt: discoveredAt ?? this.discoveredAt,
      rssi: rssi ?? this.rssi,
      additionalInfo: additionalInfo ?? this.additionalInfo,
    );
  }

  @override
  List<Object?> get props => [
        id,
        name,
        ipAddress,
        port,
        macAddress,
        version,
        model,
        discoveredAt,
        rssi,
        additionalInfo,
      ];

  @override
  String toString() {
    return 'EcoTrackerDeviceModel(id: $id, name: $name, ip: $ipAddress:$port)';
  }
}
