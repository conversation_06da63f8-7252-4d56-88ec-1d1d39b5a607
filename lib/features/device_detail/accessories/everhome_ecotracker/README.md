# EverHome EcoTracker mDNS 设备扫描功能

## 功能概述

这个模块实现了通过 mDNS (Multicast DNS) 协议扫描和发现 EverHome EcoTracker 设备的功能。用户可以在同一局域网内自动发现并连接到 EcoTracker 环境监测设备。

## 主要特性

- **自动设备发现**: 使用 mDNS 协议自动扫描局域网内的 EcoTracker 设备
- **实时状态更新**: 显示扫描进度、剩余时间和发现的设备数量
- **设备信息展示**: 显示设备名称、IP 地址、端口、型号等详细信息
- **设备连接**: 支持连接到发现的设备
- **BLoC 架构**: 使用 BLoC 模式管理状态，确保代码的可维护性

## 文件结构

```
everhome_ecotracker/
├── bloc/
│   ├── bloc.dart                    # BLoC 导出文件
│   ├── ecotracker_scan_bloc.dart    # 主要的 BLoC 逻辑
│   ├── ecotracker_scan_event.dart   # 扫描事件定义
│   └── ecotracker_scan_state.dart   # 扫描状态定义
├── model/
│   └── ecotracker_device_model.dart # 设备数据模型
├── service/
│   └── mdns_scan_service.dart       # mDNS 扫描服务
├── page/
│   └── everhome_ecotracker_page.dart # 主页面 UI
├── routes/
│   └── everhome_ecotracker_page_route.dart # 路由配置
├── test_ecotracker.dart             # 测试入口
└── README.md                        # 说明文档
```

## 核心组件

### 1. MdnsScanService
负责 mDNS 设备扫描的核心服务类：
- 扫描 `_ecotracker._tcp.local` 服务
- 解析 PTR、SRV、TXT 记录
- 提供设备发现的流式数据

### 2. EcoTrackerScanBloc
使用 BLoC 模式管理扫描状态：
- 处理扫描开始/停止事件
- 管理设备连接状态
- 提供倒计时功能

### 3. EcoTrackerDeviceModel
设备数据模型，包含：
- 设备 ID、名称、IP 地址、端口
- MAC 地址、版本、型号信息
- 发现时间和附加信息

## 使用方法

### 1. 导航到页面
```dart
Navigator.push(
  context,
  MaterialPageRoute(
    builder: (context) => const EverHomeEcotrackerPage(),
  ),
);
```

### 2. 扫描设备
- 点击"开始扫描"按钮开始搜索设备
- 页面会显示扫描进度和剩余时间
- 发现的设备会实时显示在列表中

### 3. 连接设备
- 在设备列表中点击"连接设备"按钮
- 系统会尝试连接到选中的设备
- 连接成功后会显示相应的状态

## 技术实现

### mDNS 协议
使用 `multicast_dns` 包实现设备发现：
```dart
// 查找 PTR 记录
_client!.lookup<PtrResourceRecord>(
  ResourceRecordQuery.serverPointer('_ecotracker._tcp.local'),
)
```

### BLoC 状态管理
```dart
// 开始扫描
context.read<EcoTrackerScanBloc>().add(const StartScanEvent());

// 监听状态变化
BlocConsumer<EcoTrackerScanBloc, EcoTrackerScanState>(
  listener: (context, state) {
    // 处理状态变化
  },
  builder: (context, state) {
    // 构建 UI
  },
)
```

## 依赖项

- `multicast_dns: ^0.3.2+7` - mDNS 协议支持
- `flutter_bloc: ^7.2.0` - 状态管理
- `loading_animation_widget` - 加载动画
- `flutter_screenutil` - 屏幕适配

## 注意事项

1. **网络权限**: 确保应用有网络访问权限
2. **同一网络**: 设备和手机必须在同一局域网内
3. **设备支持**: EcoTracker 设备需要支持 mDNS 协议
4. **超时设置**: 默认扫描超时时间为 30 秒

## 测试

运行测试应用：
```dart
// 使用 test_ecotracker.dart 文件
flutter run lib/features/device_detail/accessories/everhome_ecotracker/test_ecotracker.dart
```

## 扩展功能

可以考虑添加的功能：
- 设备过滤和搜索
- 设备收藏和历史记录
- 批量设备操作
- 设备状态监控
- 自定义扫描参数
