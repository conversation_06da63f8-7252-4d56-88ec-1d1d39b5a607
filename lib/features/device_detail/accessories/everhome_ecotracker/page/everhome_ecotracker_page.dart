import 'package:flutter_basic/components/components.dart';
import 'package:flutter_basic/installer/theme/button.dart';
import 'package:flutter_basic/platform/platform.dart';
import 'package:flutter_basic/platform/utils/color_utils.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class EverHomeEcotrackerPage extends StatefulWidget {
  const EverHomeEcotrackerPage({super.key});

  @override
  State<EverHomeEcotrackerPage> createState() => _EverHomeEcotrackerPageState();
}

class _EverHomeEcotrackerPageState extends State<EverHomeEcotrackerPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        titleText: 'EverHome Ecotracker',
      ),
      backgroundColor: ColorsUtil.backgroundColor,
      body: SafeArea(
        child: Padding(
          padding: EdgeInsets.all(20.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildWelcomeCard(),
              Si<PERSON><PERSON><PERSON>(height: 20.h),
              _buildFeaturesList(),
              const Spacer(),
              _buildGetStartedButton(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildWelcomeCard() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(20.w),
      decoration: BoxDecoration(
        color: ColorsUtil.windowBgColor,
        borderRadius: BorderRadius.circular(12.w),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 60.w,
                height: 60.w,
                decoration: BoxDecoration(
                  color: ColorsUtil.themeColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(30.w),
                ),
                child: Icon(
                  Icons.eco,
                  color: ColorsUtil.themeColor,
                  size: 30.w,
                ),
              ),
              SizedBox(width: 15.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'EverHome Ecotracker',
                      style: TextStyle(
                        fontSize: 18.sp,
                        fontWeight: FontWeight.bold,
                        color: ColorsUtil.textColor,
                      ),
                    ),
                    SizedBox(height: 5.h),
                    Text(
                      '智能环境监测设备',
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: ColorsUtil.contentColor,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          SizedBox(height: 15.h),
          Text(
            '欢迎使用 EverHome Ecotracker！这是一款先进的环境监测设备，可以帮助您实时监控家庭环境状况。',
            style: TextStyle(
              fontSize: 14.sp,
              color: ColorsUtil.contentColor,
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFeaturesList() {
    final features = [
      {
        'icon': Icons.thermostat,
        'title': '温度监测',
        'description': '实时监测室内温度变化',
      },
      {
        'icon': Icons.water_drop,
        'title': '湿度监测',
        'description': '精确测量空气湿度',
      },
      {
        'icon': Icons.air,
        'title': '空气质量',
        'description': '监测PM2.5和空气质量指数',
      },
      {
        'icon': Icons.lightbulb,
        'title': '光照强度',
        'description': '测量环境光照强度',
      },
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '主要功能',
          style: TextStyle(
            fontSize: 16.sp,
            fontWeight: FontWeight.w600,
            color: ColorsUtil.textColor,
          ),
        ),
        SizedBox(height: 15.h),
        ...features.map((feature) => _buildFeatureItem(
              icon: feature['icon'] as IconData,
              title: feature['title'] as String,
              description: feature['description'] as String,
            )),
      ],
    );
  }

  Widget _buildFeatureItem({
    required IconData icon,
    required String title,
    required String description,
  }) {
    return Container(
      margin: EdgeInsets.only(bottom: 12.h),
      padding: EdgeInsets.all(15.w),
      decoration: BoxDecoration(
        color: ColorsUtil.windowBgColor,
        borderRadius: BorderRadius.circular(8.w),
        border: Border.all(color: ColorsUtil.dividerColor),
      ),
      child: Row(
        children: [
          Container(
            width: 40.w,
            height: 40.w,
            decoration: BoxDecoration(
              color: ColorsUtil.themeColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(20.w),
            ),
            child: Icon(
              icon,
              color: ColorsUtil.themeColor,
              size: 20.w,
            ),
          ),
          SizedBox(width: 15.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w500,
                    color: ColorsUtil.textColor,
                  ),
                ),
                SizedBox(height: 3.h),
                Text(
                  description,
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: ColorsUtil.contentColor,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGetStartedButton() {
    return SizedBox(
      width: double.infinity,
      child: PrimaryButton(
        onPressed: () {
          // TODO: 实现开始配置功能
          CustomToast.showToast(context, '功能开发中，敬请期待！');
        },
        child: Text(
          '开始配置',
          style: TextStyle(
            fontSize: 16.sp,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }
}
