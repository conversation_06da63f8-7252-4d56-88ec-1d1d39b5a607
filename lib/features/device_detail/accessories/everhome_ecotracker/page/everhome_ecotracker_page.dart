import 'package:flutter_basic/installer/theme/button.dart';
import 'package:flutter_basic/platform/platform.dart';
import 'package:flutter_basic/platform/utils/color_utils.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:loading_animation_widget/loading_animation_widget.dart';

import '../bloc/ecotracker_scan_bloc.dart';
import '../bloc/ecotracker_scan_event.dart';
import '../bloc/ecotracker_scan_state.dart';
import '../model/ecotracker_device_model.dart';

class EverHomeEcotrackerPage extends StatefulWidget {
  const EverHomeEcotrackerPage({super.key});

  @override
  State<EverHomeEcotrackerPage> createState() => _EverHomeEcotrackerPageState();
}

class _EverHomeEcotrackerPageState extends State<EverHomeEcotrackerPage> {
  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => EcoTrackerScanBloc(),
      child: Builder(
        builder: (context) => Scaffold(
          appBar: CustomAppBar(
            titleText: 'EverHome Ecotracker',
          ),
          backgroundColor: ColorsUtil.backgroundColor,
          body: SafeArea(
            child: BlocConsumer<EcoTrackerScanBloc, EcoTrackerScanState>(
              listener: (context, state) {
                if (state.connectStatus == ConnectStatus.connected) {
                  CustomToast.showToast(context, '设备连接成功！');
                } else if (state.connectStatus == ConnectStatus.failed) {
                  CustomToast.showToast(context, state.error ?? '设备连接失败');
                }
              },
              builder: (context, state) {
                return Padding(
                  padding: EdgeInsets.all(20.w),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildScanStatusCard(state, context),
                      SizedBox(height: 20.h),
                      if (state.scanStatus == ScanStatus.scanning ||
                          state.devices.isNotEmpty)
                        _buildDevicesList(state, context)
                      else
                        _buildWelcomeContent(),
                    ],
                  ),
                );
              },
            ),
          ),
        ),
      ),
    );
  }

  /// 构建扫描状态卡片
  Widget _buildScanStatusCard(EcoTrackerScanState state, BuildContext context) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(20.w),
      decoration: BoxDecoration(
        color: ColorsUtil.windowBgColor,
        borderRadius: BorderRadius.circular(12.w),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 60.w,
                height: 60.w,
                decoration: BoxDecoration(
                  color: _getScanStatusColor(state.scanStatus).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(30.w),
                ),
                child: state.scanStatus == ScanStatus.scanning
                    ? LoadingAnimationWidget.staggeredDotsWave(
                        color: _getScanStatusColor(state.scanStatus),
                        size: 30.w,
                      )
                    : Icon(
                        _getScanStatusIcon(state.scanStatus),
                        color: _getScanStatusColor(state.scanStatus),
                        size: 30.w,
                      ),
              ),
              SizedBox(width: 15.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _getScanStatusTitle(state.scanStatus),
                      style: TextStyle(
                        fontSize: 18.sp,
                        fontWeight: FontWeight.bold,
                        color: ColorsUtil.textColor,
                      ),
                    ),
                    SizedBox(height: 5.h),
                    Text(
                      _getScanStatusDescription(state),
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: ColorsUtil.contentColor,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          if (state.scanStatus == ScanStatus.scanning) ...[
            SizedBox(height: 15.h),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  '剩余时间: ${state.remainingTime}秒',
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: ColorsUtil.contentColor,
                  ),
                ),
                Text(
                  '已发现: ${state.devices.length} 台设备',
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: ColorsUtil.themeColor,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ],
          SizedBox(height: 15.h),
          _buildScanActionButtons(state, context),
        ],
      ),
    );
  }

  /// 构建扫描操作按钮
  Widget _buildScanActionButtons(
      EcoTrackerScanState state, BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: PrimaryButton(
            onPressed: state.scanStatus == ScanStatus.scanning
                ? () => context
                    .read<EcoTrackerScanBloc>()
                    .add(const StopScanEvent())
                : () => context
                    .read<EcoTrackerScanBloc>()
                    .add(const StartScanEvent()),
            child: Text(
              state.scanStatus == ScanStatus.scanning ? '停止扫描' : '开始扫描',
              style: TextStyle(
                fontSize: 14.sp,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ),
        if (state.devices.isNotEmpty) ...[
          SizedBox(width: 10.w),
          Expanded(
            child: OutlinedButton(
              onPressed: () => context
                  .read<EcoTrackerScanBloc>()
                  .add(const RefreshScanEvent()),
              child: Text(
                '刷新',
                style: TextStyle(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w500,
                  color: ColorsUtil.themeColor,
                ),
              ),
            ),
          ),
        ],
      ],
    );
  }

  /// 获取扫描状态颜色
  Color _getScanStatusColor(ScanStatus status) {
    switch (status) {
      case ScanStatus.initial:
        return ColorsUtil.contentColor;
      case ScanStatus.scanning:
        return ColorsUtil.themeColor;
      case ScanStatus.success:
        return Colors.green;
      case ScanStatus.failure:
        return Colors.red;
      case ScanStatus.stopped:
        return Colors.orange;
    }
  }

  /// 获取扫描状态图标
  IconData _getScanStatusIcon(ScanStatus status) {
    switch (status) {
      case ScanStatus.initial:
        return Icons.search;
      case ScanStatus.scanning:
        return Icons.wifi_find;
      case ScanStatus.success:
        return Icons.check_circle;
      case ScanStatus.failure:
        return Icons.error;
      case ScanStatus.stopped:
        return Icons.stop_circle;
    }
  }

  /// 获取扫描状态标题
  String _getScanStatusTitle(ScanStatus status) {
    switch (status) {
      case ScanStatus.initial:
        return '正在查找EcoTracker设备';
      case ScanStatus.scanning:
        return '正在扫描设备...';
      case ScanStatus.success:
        return '扫描完成';
      case ScanStatus.failure:
        return '扫描失败';
      case ScanStatus.stopped:
        return '扫描已停止';
    }
  }

  /// 获取扫描状态描述
  String _getScanStatusDescription(EcoTrackerScanState state) {
    switch (state.scanStatus) {
      case ScanStatus.initial:
        return '请确保手机和设备连接在同一个局域网内';
      case ScanStatus.scanning:
        return '正在搜索局域网内的EcoTracker设备...';
      case ScanStatus.success:
        return state.devices.isEmpty
            ? '未发现任何设备'
            : '发现 ${state.devices.length} 台设备';
      case ScanStatus.failure:
        return state.error ?? '扫描过程中出现错误';
      case ScanStatus.stopped:
        return '扫描已手动停止';
    }
  }

  /// 构建设备列表
  Widget _buildDevicesList(EcoTrackerScanState state, BuildContext context) {
    return Expanded(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '发现的设备',
            style: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.w600,
              color: ColorsUtil.textColor,
            ),
          ),
          SizedBox(height: 15.h),
          if (state.scanStatus == ScanStatus.scanning && state.devices.isEmpty)
            _buildScanningIndicator()
          else if (state.devices.isEmpty)
            _buildEmptyDevicesList()
          else
            Expanded(
              child: ListView.builder(
                itemCount: state.devices.length,
                itemBuilder: (context, index) {
                  final device = state.devices[index];
                  return _buildDeviceCard(device, state, context);
                },
              ),
            ),
        ],
      ),
    );
  }

  /// 构建扫描指示器
  Widget _buildScanningIndicator() {
    return SizedBox(
      height: 200.h,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            LoadingAnimationWidget.staggeredDotsWave(
              color: ColorsUtil.themeColor,
              size: 50.w,
            ),
            SizedBox(height: 20.h),
            Text(
              '正在搜索设备...',
              style: TextStyle(
                fontSize: 14.sp,
                color: ColorsUtil.contentColor,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建空设备列表
  Widget _buildEmptyDevicesList() {
    return SizedBox(
      height: 200.h,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.devices_other,
              size: 60.w,
              color: ColorsUtil.contentColor.withOpacity(0.5),
            ),
            SizedBox(height: 15.h),
            Text(
              '未发现任何设备',
              style: TextStyle(
                fontSize: 16.sp,
                fontWeight: FontWeight.w500,
                color: ColorsUtil.textColor,
              ),
            ),
            SizedBox(height: 8.h),
            Text(
              '请确保设备已开启并连接到同一网络',
              style: TextStyle(
                fontSize: 12.sp,
                color: ColorsUtil.contentColor,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// 构建设备卡片
  Widget _buildDeviceCard(EcoTrackerDeviceModel device,
      EcoTrackerScanState state, BuildContext context) {
    final isConnecting = state.connectStatus == ConnectStatus.connecting;
    final isConnected = state.connectedDeviceId == device.id;

    return Container(
      margin: EdgeInsets.only(bottom: 12.h),
      padding: EdgeInsets.all(15.w),
      decoration: BoxDecoration(
        color: ColorsUtil.windowBgColor,
        borderRadius: BorderRadius.circular(12.w),
        border: Border.all(
          color: isConnected ? ColorsUtil.themeColor : ColorsUtil.dividerColor,
          width: isConnected ? 2 : 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 50.w,
                height: 50.w,
                decoration: BoxDecoration(
                  color: ColorsUtil.themeColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(25.w),
                ),
                child: Icon(
                  Icons.eco,
                  color: ColorsUtil.themeColor,
                  size: 25.w,
                ),
              ),
              SizedBox(width: 15.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      device.name,
                      style: TextStyle(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.w600,
                        color: ColorsUtil.textColor,
                      ),
                    ),
                    SizedBox(height: 4.h),
                    Text(
                      '${device.ipAddress}:${device.port}',
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: ColorsUtil.contentColor,
                      ),
                    ),
                    if (device.model != null) ...[
                      SizedBox(height: 2.h),
                      Text(
                        '型号: ${device.model}',
                        style: TextStyle(
                          fontSize: 11.sp,
                          color: ColorsUtil.contentColor,
                        ),
                      ),
                    ],
                  ],
                ),
              ),
              if (isConnected)
                Icon(
                  Icons.check_circle,
                  color: Colors.green,
                  size: 20.w,
                ),
            ],
          ),
          SizedBox(height: 15.h),
          SizedBox(
            width: double.infinity,
            child: PrimaryButton(
              onPressed: isConnecting
                  ? null
                  : () {
                      context.read<EcoTrackerScanBloc>().add(
                            ConnectDeviceEvent(
                              deviceId: device.id,
                              deviceName: device.name,
                              ipAddress: device.ipAddress,
                              port: device.port,
                            ),
                          );
                    },
              child: isConnecting
                  ? Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SizedBox(
                          width: 16.w,
                          height: 16.w,
                          child: const CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor:
                                AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        ),
                        SizedBox(width: 8.w),
                        Text(
                          '连接中...',
                          style: TextStyle(
                            fontSize: 14.sp,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    )
                  : Text(
                      isConnected ? '已连接' : '连接设备',
                      style: TextStyle(
                        fontSize: 14.sp,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建欢迎内容
  Widget _buildWelcomeContent() {
    return Expanded(
      child: Column(
        children: [
          _buildWelcomeCard(),
          SizedBox(height: 20.h),
          _buildFeaturesList(),
        ],
      ),
    );
  }

  Widget _buildWelcomeCard() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(20.w),
      decoration: BoxDecoration(
        color: ColorsUtil.windowBgColor,
        borderRadius: BorderRadius.circular(12.w),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 60.w,
                height: 60.w,
                decoration: BoxDecoration(
                  color: ColorsUtil.themeColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(30.w),
                ),
                child: Icon(
                  Icons.eco,
                  color: ColorsUtil.themeColor,
                  size: 30.w,
                ),
              ),
              SizedBox(width: 15.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'EverHome Ecotracker',
                      style: TextStyle(
                        fontSize: 18.sp,
                        fontWeight: FontWeight.bold,
                        color: ColorsUtil.textColor,
                      ),
                    ),
                    SizedBox(height: 5.h),
                    Text(
                      '智能环境监测设备',
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: ColorsUtil.contentColor,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          SizedBox(height: 15.h),
          Text(
            '欢迎使用 EverHome Ecotracker！这是一款先进的环境监测设备，可以帮助您实时监控家庭环境状况。',
            style: TextStyle(
              fontSize: 14.sp,
              color: ColorsUtil.contentColor,
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFeaturesList() {
    final features = [
      {
        'icon': Icons.thermostat,
        'title': '温度监测',
        'description': '实时监测室内温度变化',
      },
      {
        'icon': Icons.water_drop,
        'title': '湿度监测',
        'description': '精确测量空气湿度',
      },
      {
        'icon': Icons.air,
        'title': '空气质量',
        'description': '监测PM2.5和空气质量指数',
      },
      {
        'icon': Icons.lightbulb,
        'title': '光照强度',
        'description': '测量环境光照强度',
      },
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '主要功能',
          style: TextStyle(
            fontSize: 16.sp,
            fontWeight: FontWeight.w600,
            color: ColorsUtil.textColor,
          ),
        ),
        SizedBox(height: 15.h),
        ...features.map((feature) => _buildFeatureItem(
              icon: feature['icon'] as IconData,
              title: feature['title'] as String,
              description: feature['description'] as String,
            )),
      ],
    );
  }

  Widget _buildFeatureItem({
    required IconData icon,
    required String title,
    required String description,
  }) {
    return Container(
      margin: EdgeInsets.only(bottom: 12.h),
      padding: EdgeInsets.all(15.w),
      decoration: BoxDecoration(
        color: ColorsUtil.windowBgColor,
        borderRadius: BorderRadius.circular(8.w),
        border: Border.all(color: ColorsUtil.dividerColor),
      ),
      child: Row(
        children: [
          Container(
            width: 40.w,
            height: 40.w,
            decoration: BoxDecoration(
              color: ColorsUtil.themeColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(20.w),
            ),
            child: Icon(
              icon,
              color: ColorsUtil.themeColor,
              size: 20.w,
            ),
          ),
          SizedBox(width: 15.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w500,
                    color: ColorsUtil.textColor,
                  ),
                ),
                SizedBox(height: 3.h),
                Text(
                  description,
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: ColorsUtil.contentColor,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
