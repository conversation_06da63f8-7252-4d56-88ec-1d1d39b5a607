import 'package:equatable/equatable.dart';

abstract class EcoTrackerScanEvent extends Equatable {
  const EcoTrackerScanEvent();

  @override
  List<Object?> get props => [];
}

/// 开始扫描设备
class StartScanEvent extends EcoTrackerScanEvent {
  const StartScanEvent();
}

/// 停止扫描设备
class StopScanEvent extends EcoTrackerScanEvent {
  const StopScanEvent();
}

/// 重新扫描设备
class RefreshScanEvent extends EcoTrackerScanEvent {
  const RefreshScanEvent();
}

/// 连接设备
class ConnectDeviceEvent extends EcoTrackerScanEvent {
  final String deviceId;
  final String deviceName;
  final String ipAddress;
  final int port;

  const ConnectDeviceEvent({
    required this.deviceId,
    required this.deviceName,
    required this.ipAddress,
    required this.port,
  });

  @override
  List<Object?> get props => [deviceId, deviceName, ipAddress, port];
}

/// 清除扫描结果
class ClearScanResultsEvent extends EcoTrackerScanEvent {
  const ClearScanResultsEvent();
}
