import 'package:equatable/equatable.dart';
import '../model/ecotracker_device_model.dart';

enum ScanStatus {
  initial,
  scanning,
  success,
  failure,
  stopped,
}

enum ConnectStatus {
  initial,
  connecting,
  connected,
  failed,
}

class EcoTrackerScanState extends Equatable {
  final ScanStatus scanStatus;
  final ConnectStatus connectStatus;
  final List<EcoTrackerDeviceModel> devices;
  final String? error;
  final String? connectedDeviceId;
  final int scanDuration; // 扫描持续时间（秒）
  final int remainingTime; // 剩余扫描时间（秒）

  const EcoTrackerScanState({
    this.scanStatus = ScanStatus.initial,
    this.connectStatus = ConnectStatus.initial,
    this.devices = const [],
    this.error,
    this.connectedDeviceId,
    this.scanDuration = 30,
    this.remainingTime = 0,
  });

  EcoTrackerScanState copyWith({
    ScanStatus? scanStatus,
    ConnectStatus? connectStatus,
    List<EcoTrackerDeviceModel>? devices,
    String? error,
    String? connectedDeviceId,
    int? scanDuration,
    int? remainingTime,
  }) {
    return EcoTrackerScanState(
      scanStatus: scanStatus ?? this.scanStatus,
      connectStatus: connectStatus ?? this.connectStatus,
      devices: devices ?? this.devices,
      error: error ?? this.error,
      connectedDeviceId: connectedDeviceId ?? this.connectedDeviceId,
      scanDuration: scanDuration ?? this.scanDuration,
      remainingTime: remainingTime ?? this.remainingTime,
    );
  }

  @override
  List<Object?> get props => [
        scanStatus,
        connectStatus,
        devices,
        error,
        connectedDeviceId,
        scanDuration,
        remainingTime,
      ];
}
